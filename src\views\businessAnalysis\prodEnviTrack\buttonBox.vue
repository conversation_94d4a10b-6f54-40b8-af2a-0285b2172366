<template>
  <div class="buttonBox">
    <el-tooltip
      v-for="(item, index) in buttons"
      :key="index"
      :content="shouldShowTooltip(item) ? item : ''"
      :disabled="!shouldShowTooltip(item)"
      :open-delay="500"
      :popper-class="getTooltipClass()"
      placement="top"
    >
      <div
        class="buttonItem"
        :class="{ active: activeIndex === index }"
        @click="clickHandle(index)"
      >
        <span class="button-text">{{ getTruncatedText(item) }}</span>
      </div>
    </el-tooltip>
  </div>
</template>
<script>
export default {
  name: "buttonBox",
  props: {
    buttons: {
      type: Array,
      default: () => [],
    },
    // 最大显示字符数，可以通过props自定义
    maxTextLength: {
      type: Number,
      default: 8, // 基于128px宽度计算的合理字符数
    },
    // 是否启用响应式宽度
    responsive: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  computed: {
    // 获取当前主题
    currentTheme() {
      return document.documentElement.getAttribute('data-theme') || 'default';
    },
  },
  methods: {
    clickHandle(index) {
      this.activeIndex = index;
      this.$emit("clickHandle", index);
    },

    /**
     * 判断是否需要显示tooltip
     * @param {String} text - 按钮文本
     * @returns {Boolean} 是否显示tooltip
     */
    shouldShowTooltip(text) {
      if (!text) return false;
      return String(text).length > this.maxTextLength;
    },

    /**
     * 获取截断后的文本
     * @param {String} text - 原始文本
     * @returns {String} 截断后的文本
     */
    getTruncatedText(text) {
      if (!text) return '';
      const stringText = String(text);
      if (stringText.length <= this.maxTextLength) {
        return stringText;
      }
      return stringText.substring(0, this.maxTextLength) + '...';
    },

    /**
     * 获取tooltip的CSS类名
     * @returns {String} CSS类名
     */
    getTooltipClass() {
      return `buttonbox-tooltip-${this.currentTheme}`;
    },
  },
};
</script>
<style lang="scss" scoped>
.buttonBox {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap; // 支持换行，提高响应式体验
  gap: 8px; // 使用gap替代margin-right，更现代的布局方式

  .buttonItem {
    width: 128px;
    height: 35px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    position: relative;

    .button-text {
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 8px; // 添加内边距，避免文字贴边
      box-sizing: border-box;
    }
  }
}

.buttonItem:hover {
  color: #fff !important;
}

// 响应式设计：在小屏幕上调整按钮宽度
@media (max-width: 768px) {
  .buttonBox {
    .buttonItem {
      width: 110px; // 小屏幕上减小按钮宽度
      font-size: 14px; // 减小字体大小
    }
  }
}

@media (max-width: 480px) {
  .buttonBox {
    .buttonItem {
      width: 100px; // 更小屏幕上进一步减小宽度
      font-size: 13px;
    }
  }
}

// 主题样式保持不变
[data-theme="dark"] .buttonItem {
  background-image: url("@/assets/tableicon/button-darkbg.png");
  color: #6ba4f4;
}

[data-theme="dark"] .active {
  background-image: url("@/assets/tableicon/buttonactive-darkbg.png") !important;
  color: #fff !important;
}

[data-theme="tint"] .buttonItem {
  background-image: url("@/assets/tableicon/button-tintbg.png");
}

[data-theme="tint"] .active {
  background-image: url("@/assets/tableicon/buttonactive-tintbg.png") !important;
  color: #fff !important;
}
</style>

<!-- 全局样式：Tooltip主题适配 -->
<style lang="scss">
// 默认主题tooltip样式
.buttonbox-tooltip-default {
  .el-tooltip__popper {
    background-color: #303133 !important;
    color: #ffffff !important;
    border: 1px solid #303133 !important;
    font-size: 14px !important;
    max-width: 300px !important;
    word-wrap: break-word !important;
  }

  .el-tooltip__popper[x-placement^="top"] .el-popper__arrow::after {
    border-top-color: #303133 !important;
  }
}

// 深色主题tooltip样式
.buttonbox-tooltip-dark {
  .el-tooltip__popper {
    background-color: #1f2937 !important;
    color: #f3f4f6 !important;
    border: 1px solid #374151 !important;
    font-size: 14px !important;
    max-width: 300px !important;
    word-wrap: break-word !important;
  }

  .el-tooltip__popper[x-placement^="top"] .el-popper__arrow::after {
    border-top-color: #1f2937 !important;
  }
}

// 浅色主题tooltip样式
.buttonbox-tooltip-tint {
  .el-tooltip__popper {
    background-color: #f8fafc !important;
    color: #1e293b !important;
    border: 1px solid #e2e8f0 !important;
    font-size: 14px !important;
    max-width: 300px !important;
    word-wrap: break-word !important;
  }

  .el-tooltip__popper[x-placement^="top"] .el-popper__arrow::after {
    border-top-color: #f8fafc !important;
  }
}
</style>
