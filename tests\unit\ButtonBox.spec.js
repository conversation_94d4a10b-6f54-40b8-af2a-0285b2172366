import { shallowMount } from '@vue/test-utils';
import ButtonBox from '@/views/businessAnalysis/prodEnviTrack/buttonBox.vue';

describe('ButtonBox.vue', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = shallowMount(ButtonBox, {
      propsData: {
        buttons: ['短文本', '这是一个比较长的文本内容', 'YC13-1']
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  describe('基础功能测试', () => {
    it('应该正确渲染按钮', () => {
      expect(wrapper.findAll('.buttonItem')).toHaveLength(3);
    });

    it('应该正确处理点击事件', async () => {
      const buttons = wrapper.findAll('.buttonItem');
      await buttons.at(1).trigger('click');
      
      expect(wrapper.vm.activeIndex).toBe(1);
      expect(wrapper.emitted().clickHandle).toBeTruthy();
      expect(wrapper.emitted().clickHandle[0]).toEqual([1]);
    });

    it('应该正确设置active状态', async () => {
      wrapper.setData({ activeIndex: 1 });
      await wrapper.vm.$nextTick();
      
      const buttons = wrapper.findAll('.buttonItem');
      expect(buttons.at(1).classes()).toContain('active');
      expect(buttons.at(0).classes()).not.toContain('active');
    });
  });

  describe('文本截断功能测试', () => {
    it('shouldShowTooltip方法应该正确判断是否显示tooltip', () => {
      expect(wrapper.vm.shouldShowTooltip('短文本')).toBe(false);
      expect(wrapper.vm.shouldShowTooltip('这是一个比较长的文本内容')).toBe(true);
      expect(wrapper.vm.shouldShowTooltip('')).toBe(false);
      expect(wrapper.vm.shouldShowTooltip(null)).toBe(false);
    });

    it('getTruncatedText方法应该正确截断文本', () => {
      expect(wrapper.vm.getTruncatedText('短文本')).toBe('短文本');
      expect(wrapper.vm.getTruncatedText('这是一个比较长的文本内容')).toBe('这是一个比较长的...');
      expect(wrapper.vm.getTruncatedText('')).toBe('');
    });

    it('应该根据maxTextLength属性调整截断长度', async () => {
      wrapper.setProps({ maxTextLength: 4 });
      await wrapper.vm.$nextTick();
      
      expect(wrapper.vm.getTruncatedText('这是一个比较长的文本')).toBe('这是一个...');
      expect(wrapper.vm.shouldShowTooltip('这是一个比较长的文本')).toBe(true);
    });
  });

  describe('主题功能测试', () => {
    it('getTooltipClass方法应该返回正确的CSS类名', () => {
      // 模拟不同主题
      document.documentElement.setAttribute('data-theme', 'dark');
      expect(wrapper.vm.getTooltipClass()).toBe('buttonbox-tooltip-dark');

      document.documentElement.setAttribute('data-theme', 'tint');
      expect(wrapper.vm.getTooltipClass()).toBe('buttonbox-tooltip-tint');

      document.documentElement.removeAttribute('data-theme');
      expect(wrapper.vm.getTooltipClass()).toBe('buttonbox-tooltip-tint');
    });

    it('currentTheme计算属性应该正确获取当前主题', () => {
      document.documentElement.setAttribute('data-theme', 'dark');
      expect(wrapper.vm.currentTheme).toBe('dark');

      document.documentElement.removeAttribute('data-theme');
      expect(wrapper.vm.currentTheme).toBe('tint');
    });
  });

  describe('Props验证测试', () => {
    it('应该接受buttons数组prop', () => {
      const newButtons = ['新按钮1', '新按钮2'];
      wrapper.setProps({ buttons: newButtons });
      
      expect(wrapper.vm.buttons).toEqual(newButtons);
    });

    it('应该接受maxTextLength数字prop', () => {
      wrapper.setProps({ maxTextLength: 10 });
      expect(wrapper.vm.maxTextLength).toBe(10);
    });

    it('应该接受responsive布尔prop', () => {
      wrapper.setProps({ responsive: true });
      expect(wrapper.vm.responsive).toBe(true);
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空按钮数组', () => {
      wrapper.setProps({ buttons: [] });
      expect(wrapper.findAll('.buttonItem')).toHaveLength(0);
    });

    it('应该处理包含特殊字符的文本', () => {
      const specialText = '特殊字符@#$%^&*()测试';
      expect(wrapper.vm.getTruncatedText(specialText)).toBe('特殊字符@#$%...');
    });

    it('应该处理数字类型的按钮文本', () => {
      expect(wrapper.vm.getTruncatedText(123456789)).toBe('12345678...');
      expect(wrapper.vm.shouldShowTooltip(123456789)).toBe(true);
    });
  });
});
