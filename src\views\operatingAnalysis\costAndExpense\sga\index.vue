<template>
  <div class="sga">
    <div class="content-left">
      <div class="main-indicators">
        <ChartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </ChartBox>
      </div>
      <div class="statistics-box">
        <ChartBox :title="'分项统计-管理费'">
          <ButtonBox :buttons="magBtns"></ButtonBox>
          <div class="table-box">
            <CommonTable
              :tableData="displayTableData1"
              :colums="colums1"
              :showIndexColumn="false"
              :border="false"
              :tableHeight="'auto'"
              :maxHeight="220"
            />
            <!-- 紧凑型分页器 -->
            <div class="compact-pagination" v-if="tableData1.length > pageSize1">
              <el-pagination
                small
                background
                layout="prev, pager, next"
                :current-page="currentPage1"
                :page-size="pageSize1"
                :total="tableData1.length"
                @current-change="handlePage1Change"
                :hide-on-single-page="false"
              />
              <span class="page-info">{{ getPageInfo(currentPage1, pageSize1, tableData1.length) }}</span>
            </div>
          </div>
        </ChartBox>
      </div>
      <div class="statistics-box">
        <ChartBox :title="'分项统计-销售费'">
          <CarouselBtn :buttons="buttons" />
          <div class="table-box">
            <CommonTable
              :tableData="displayTableData2"
              :colums="colums2"
              :showIndexColumn="false"
              :border="false"
              :tableHeight="'auto'"
              :maxHeight="220"
            />
            <!-- 紧凑型分页器 -->
            <div class="compact-pagination" v-if="tableData2.length > pageSize2">
              <el-pagination
                small
                background
                layout="prev, pager, next"
                :current-page="currentPage2"
                :page-size="pageSize2"
                :total="tableData2.length"
                @current-change="handlePage2Change"
                :hide-on-single-page="false"
              />
              <span class="page-info">{{ getPageInfo(currentPage2, pageSize2, tableData2.length) }}</span>
            </div>
          </div>
        </ChartBox>
      </div>
      <div class="cost-structure">
        <ChartBox :title="'费用结构'">
          <ExpenseStructure />
        </ChartBox>
      </div>
    </div>
    <div class="content-right">
      <div class="budget-execution">
        <ChartBox :title="'预算执行'">
          <BudgetExecution />
        </ChartBox>
      </div>
      <div class="motivation-box">
        <ChartBox :title="'同比增减动因'">
          <MotivationChart
            :salesChartData="motivationData.sales"
            :managementChartData="motivationData.management"
          />
        </ChartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ItemCard from "../../components/ItemCard.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import ButtonBox from "@/views/businessAnalysis/prodEnviTrack/buttonBox.vue";
import ExpenseStructure from "./expenseStructure/index.vue";
import BudgetExecution from "./budgetExecution/index.vue";
import MotivationChart from "./motivationChart/index.vue";
export default {
  name: "sga",
  components: {
    CommonTable,
    DatePicker,
    ItemCard,
    CarouselBtn,
    ButtonBox,
    ExpenseStructure,
    BudgetExecution,
    MotivationChart,
  },
  data() {
    return {
      newDateValue: "",
      cardData: [
        { title: "管理费用", value: "35", unit: "万元" },
        { title: "销售费用", value: "3000", unit: "万元" },
      ],
      // 分页相关数据
      currentPage1: 1,
      currentPage2: 1,
      pageSize1: 4, // 每页显示4条数据，适配卡片高度
      pageSize2: 4,
      // 同比增减动因数据（可以从API获取）
      motivationData: {
        sales: [], // 如果为空，组件将使用默认数据
        management: [], // 如果为空，组件将使用默认数据
      },
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      magBtns: ["作业公司", "海分机关-陵水切块","崖城机关"],
      colums1: [
        {
          label: "科目",
          prop: "subject",
        },
        {
          label: "同期完成",
          children: [
            {
              label: "本年累计",
              prop: "thisYearTotal",
            },
            {
              label: "同期预算",
              prop: "lastYearTotal",
            },
            {
              label: "差额",
              prop: "difference",
            },
            {
              label: "同期完成率",
              prop: "completionRate",
            },
          ],
        },
        {
          label: "全年完成",
          children: [
            {
              label: "全年预算",
              prop: "yearTotal",
            },
            {
              label: "全年完成率",
              prop: "completionRateYear",
            },
          ],
        },
      ],
      colums2: [
        {
          label: "科目",
          prop: "subject",
        },
        {
          label: "同期完成",
          children: [
            {
              label: "本年累计",
              prop: "thisYearTotal",
            },
            {
              label: "同期预算",
              prop: "lastYearTotal",
            },
            {
              label: "差额",
              prop: "difference",
            },
            {
              label: "同期完成率",
              prop: "completionRate",
            },
          ],
        },
        {
          label: "全年完成",
          children: [
            {
              label: "全年预算",
              prop: "yearTotal",
            },
            {
              label: "全年完成率",
              prop: "completionRateYear",
            },
          ],
        },
      ],
      tableData1: [
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "10%",
          yearTotal: "35",
          completionRateYear: "10%",
        },
      ],
      tableData2: [
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
      ],
    };
  },
  computed: {
    // 计算当前页显示的表格1数据
    displayTableData1() {
      const start = (this.currentPage1 - 1) * this.pageSize1;
      const end = start + this.pageSize1;
      return this.tableData1.slice(start, end);
    },
    // 计算当前页显示的表格2数据
    displayTableData2() {
      const start = (this.currentPage2 - 1) * this.pageSize2;
      const end = start + this.pageSize2;
      return this.tableData2.slice(start, end);
    },
  },
  methods: {
    logChange(type, value) {
      console.log(`${type}:`, value);
      // 这里可以添加日期变化的处理逻辑
      // 当日期变化时，可以重新获取同比增减动因数据
      this.fetchMotivationData();
    },

    // 获取同比增减动因数据的方法
    async fetchMotivationData() {
      try {
        // 这里可以调用实际的API
        // const response = await api.getMotivationData(this.newDateValue);
        // this.motivationData = response.data;

        // 目前使用默认数据，实际项目中可以替换为API调用
        console.log("获取同比增减动因数据...");
      } catch (error) {
        console.error("获取同比增减动因数据失败:", error);
      }
    },

    // 分页处理方法
    handlePage1Change(page) {
      this.currentPage1 = page;
    },
    handlePage2Change(page) {
      this.currentPage2 = page;
    },

    // 获取分页信息文本
    getPageInfo(currentPage, pageSize, total) {
      const start = (currentPage - 1) * pageSize + 1;
      const end = Math.min(currentPage * pageSize, total);
      return `${start}-${end} / ${total}`;
    },
  },

  // 组件挂载时获取初始数据
  mounted() {
    this.fetchMotivationData();
  },
};
</script>
<style lang="scss" scoped>
.sga {
  display: flex;
  height: 100%;
  gap: 10px;

  .content-left {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;

    .main-indicators {
      flex: 1;
      min-width: 0;
      min-height: 300px;

      .card-box {
        display: flex;
        justify-content: space-between;
        margin: 8px 20px;
        flex: 1;
        min-height: 0;
        gap: 12px;

        .item-card {
          flex: 1;
          min-width: 0;
          height: 100%;
        }
      }
    }

    .statistics-box {
      margin-top: 10px;
      flex: 1;
      min-height: 300px;

      .table-box {
        margin: 12px 16px;
        flex: 1;
        overflow: visible;
        display: flex;
        flex-direction: column;

        // 紧凑型分页器样式
        .compact-pagination {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;
          padding: 4px 8px;
          background: rgba(255, 255, 255, 0.02);
          border-radius: 4px;
          border: 1px solid rgba(255, 255, 255, 0.1);

          .page-info {
            font-size: 12px;
            color: #8c8c8c;
            white-space: nowrap;
          }

          // 小型分页器样式优化
          ::v-deep .el-pagination {
            .el-pager li {
              min-width: 24px;
              height: 24px;
              line-height: 24px;
              margin: 0 2px;
              font-size: 12px;
            }

            .btn-prev, .btn-next {
              min-width: 24px;
              height: 24px;
              line-height: 24px;
              margin: 0 2px;
            }

            // 当前页样式
            .el-pager li.active {
              background-color: #409eff;
              color: #fff;
            }
          }
        }
      }
    }

    .cost-structure {
      flex: 1;
      min-height: 400px;
      margin-top: 10px;
    }
  }
  .content-right {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    margin-left: 10px;

    .budget-execution {
      flex: 1;
      min-height: 650px;
      margin-bottom: 10px;
    }

    .motivation-box {
      flex: 1;
      min-height: 450px;
    }
  }
}

// 移除表格右边框
::v-deep .el-table--border::after {
  width: 0 !important;
}

::v-deep .el-table th:nth-last-child(2) {
  border-right: none !important;
}

// 主题适配样式
[data-theme="dark"] .statistics-box .compact-pagination {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);

  .page-info {
    color: #a0a0a0;
  }

  ::v-deep .el-pagination {
    .el-pager li {
      background-color: transparent;
      color: #a0a0a0;

      &:hover {
        color: #409eff;
      }

      &.active {
        background-color: #409eff;
        color: #fff;
      }
    }

    .btn-prev, .btn-next {
      background-color: transparent;
      color: #a0a0a0;

      &:hover {
        color: #409eff;
      }
    }
  }
}

[data-theme="tint"] .statistics-box .compact-pagination {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.1);

  .page-info {
    color: #666;
  }

  ::v-deep .el-pagination {
    .el-pager li {
      background-color: transparent;
      color: #666;

      &:hover {
        color: #409eff;
      }

      &.active {
        background-color: #409eff;
        color: #fff;
      }
    }

    .btn-prev, .btn-next {
      background-color: transparent;
      color: #666;

      &:hover {
        color: #409eff;
      }
    }
  }
}
</style>
