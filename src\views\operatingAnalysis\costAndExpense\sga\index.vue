<template>
  <div class="sga">
    <div class="content-left">
      <div class="main-indicators">
        <ChartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </ChartBox>
      </div>
      <div class="statistics-box">
        <ChartBox :title="'分项统计-管理费'">
          <ButtonBox :buttons="magBtns"></ButtonBox>
          <div class="table-box">
            <CommonTable
              :tableData="tableData1"
              :colums="colums1"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </ChartBox>
      </div>
      <div class="statistics-box">
        <ChartBox :title="'分项统计-销售费'">
          <CarouselBtn :buttons="buttons" />
          <div class="table-box">
            <CommonTable
              :tableData="tableData2"
              :colums="colums2"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </ChartBox>
      </div>
      <div class="cost-structure">
        <ChartBox :title="'费用结构'">
          <ExpenseStructure />
        </ChartBox>
      </div>
    </div>
    <div class="content-right">
      <div class="budget-execution">
        <ChartBox :title="'预算执行'">
          <BudgetExecution />
        </ChartBox>
      </div>
      <div class="motivation-box">
        <ChartBox :title="'同比增减动因'">
          <MotivationChart
            :salesChartData="motivationData.sales"
            :managementChartData="motivationData.management"
          />
        </ChartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ItemCard from "../../components/ItemCard.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import ButtonBox from "@/views/businessAnalysis/prodEnviTrack/buttonBox.vue";
import ExpenseStructure from "./expenseStructure/index.vue";
import BudgetExecution from "./budgetExecution/index.vue";
import MotivationChart from "./motivationChart/index.vue";
export default {
  name: "sga",
  components: {
    CommonTable,
    DatePicker,
    ItemCard,
    ExpenseStructure,
    BudgetExecution,
    MotivationChart,
  },
  data() {
    return {
      newDateValue: "",
      cardData: [
        { title: "管理费用", value: "35", unit: "万元" },
        { title: "销售费用", value: "3000", unit: "万元" },
      ],
      // 同比增减动因数据（可以从API获取）
      motivationData: {
        sales: [], // 如果为空，组件将使用默认数据
        management: [], // 如果为空，组件将使用默认数据
      },
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      colums1: [
        {
          label: "科目",
          prop: "subject",
        },
        {
          label: "同期完成",
          children: [
            {
              label: "本年累计",
              prop: "thisYearTotal",
            },
            {
              label: "同期预算",
              prop: "lastYearTotal",
            },
            {
              label: "差额",
              prop: "difference",
            },
            {
              label: "同期完成率",
              prop: "completionRate",
            },
          ],
        },
        {
          label: "全年完成",
          children: [
            {
              label: "全年预算",
              prop: "yearTotal",
            },
            {
              label: "全年完成率",
              prop: "completionRateYear",
            },
          ],
        },
      ],
      colums2: [
        {
          label: "科目",
          prop: "subject",
        },
        {
          label: "同期完成",
          children: [
            {
              label: "本年累计",
              prop: "thisYearTotal",
            },
            {
              label: "同期预算",
              prop: "lastYearTotal",
            },
            {
              label: "差额",
              prop: "difference",
            },
            {
              label: "同期完成率",
              prop: "completionRate",
            },
          ],
        },
        {
          label: "全年完成",
          children: [
            {
              label: "全年预算",
              prop: "yearTotal",
            },
            {
              label: "全年完成率",
              prop: "completionRateYear",
            },
          ],
        },
      ],
      tableData1: [
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
        {
          subject: "管理费",
          thisYearTotal: "35",
          lastYearTotal: "35",
          difference: "0",
          completionRate: "100%",
          yearTotal: "35",
          completionRateYear: "100%",
        },
      ],
      tableData2: [
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
        {
          subject: "销售费",
          thisYearTotal: "3000",
          lastYearTotal: "3000",
          difference: "0",
          completionRate: "100%",
          yearTotal: "3000",
          completionRateYear: "100%",
        },
      ],
    };
  },
  methods: {
    logChange(type, value) {
      console.log(`${type}:`, value);
      // 这里可以添加日期变化的处理逻辑
      // 当日期变化时，可以重新获取同比增减动因数据
      this.fetchMotivationData();
    },

    // 获取同比增减动因数据的方法
    async fetchMotivationData() {
      try {
        // 这里可以调用实际的API
        // const response = await api.getMotivationData(this.newDateValue);
        // this.motivationData = response.data;

        // 目前使用默认数据，实际项目中可以替换为API调用
        console.log("获取同比增减动因数据...");
      } catch (error) {
        console.error("获取同比增减动因数据失败:", error);
      }
    },
  },

  // 组件挂载时获取初始数据
  mounted() {
    this.fetchMotivationData();
  },
};
</script>
<style lang="scss" scoped>
.sga {
  display: flex;
  height: 100%;
  gap: 10px;

  .content-left {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;

    .main-indicators {
      flex: 1;
      min-width: 0;
      min-height: 300px;

      .card-box {
        display: flex;
        justify-content: space-between;
        margin: 8px 20px;
        flex: 1;
        min-height: 0;
        gap: 12px;

        .item-card {
          flex: 1;
          min-width: 0;
          height: 100%;
        }
      }
    }

    .statistics-box {
      margin-top: 10px;
      flex: 1;
      min-height: 300px;

      .table-box {
        margin: 12px 16px; // 减少上下边距
        flex: 1;
        overflow: visible; // 移除滚动条，让表格完整显示
        display: flex;
        flex-direction: column;
      }
    }

    .cost-structure {
      flex: 1;
      min-height: 400px;
      margin-top: 10px;
    }
  }
  .content-right {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    margin-left: 10px;

    .budget-execution {
      flex: 1;
      min-height: 650px;
      margin-bottom: 10px;
    }

    .motivation-box {
      flex: 1;
      min-height: 450px;
    }
  }
}

// 移除表格右边框
::v-deep .el-table--border::after {
  width: 0 !important;
}

::v-deep .el-table th.el-table_1_column_7 {
  border-right: none !important;
}

::v-deep .el-table th.el-table_2_column_16 {
  border-right: none !important;
}

::v-deep .el-table th.el-table__cell:nth-child(6) {
  border-right: none !important;
}
</style>
