<template>
  <div class="button-box-example">
    <h2>ButtonBox组件优化示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>1. 基础用法（默认配置）</h3>
      <ButtonBox :buttons="shortButtons" @clickHandle="handleClick" />
    </div>
    
    <!-- 长文本示例 -->
    <div class="example-section">
      <h3>2. 长文本处理（自动截断+Tooltip）</h3>
      <ButtonBox :buttons="longButtons" @clickHandle="handleClick" />
    </div>
    
    <!-- 自定义最大字符数 -->
    <div class="example-section">
      <h3>3. 自定义最大字符数</h3>
      <ButtonBox 
        :buttons="longButtons" 
        :max-text-length="6" 
        @clickHandle="handleClick" 
      />
    </div>
    
    <!-- 混合长度文本 -->
    <div class="example-section">
      <h3>4. 混合长度文本</h3>
      <ButtonBox :buttons="mixedButtons" @clickHandle="handleClick" />
    </div>
    
    <!-- 响应式测试 -->
    <div class="example-section">
      <h3>5. 响应式测试（调整浏览器窗口大小查看效果）</h3>
      <ButtonBox :buttons="responsiveButtons" @clickHandle="handleClick" />
    </div>
    
    <!-- 当前选中信息 -->
    <div class="result-section">
      <h3>当前选中：</h3>
      <p>{{ selectedButton || '暂无选择' }}</p>
    </div>
  </div>
</template>

<script>
import ButtonBox from './buttonBox.vue';

export default {
  name: 'ButtonBoxExample',
  components: {
    ButtonBox
  },
  data() {
    return {
      selectedButton: '',
      // 短文本按钮
      shortButtons: ['YC13-1', 'YC13-10', 'LS25-1', 'LS17-2'],
      
      // 长文本按钮
      longButtons: [
        '崖城13-1海上天然气开采平台',
        '崖城13-10深海钻井设备',
        '陵水25-1综合生产设施',
        '陵水17-2海底管道系统',
        '文昌16-2石油开采装置'
      ],
      
      // 混合长度文本
      mixedButtons: [
        'YC13-1',
        '崖城13-10深海钻井设备',
        'LS25-1',
        '陵水17-2海底管道系统设备',
        '文昌16-2'
      ],
      
      // 响应式测试按钮
      responsiveButtons: [
        '设备A',
        '生产设备管理系统',
        '监控',
        '海上石油天然气开采综合管理平台',
        '设备B',
        '深海钻井作业设备'
      ]
    };
  },
  methods: {
    handleClick(index) {
      // 这里可以根据当前使用的按钮数组来确定选中的内容
      // 为了演示，我们使用混合按钮数组
      this.selectedButton = this.mixedButtons[index] || `按钮 ${index}`;
      console.log('选中按钮:', this.selectedButton);
    }
  }
};
</script>

<style lang="scss" scoped>
.button-box-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }
  
  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
    
    h3 {
      color: #555;
      margin-bottom: 15px;
      font-size: 16px;
    }
  }
  
  .result-section {
    margin-top: 30px;
    padding: 15px;
    background-color: #e8f5e8;
    border-radius: 8px;
    border-left: 4px solid #4caf50;
    
    h3 {
      color: #2e7d32;
      margin-bottom: 10px;
    }
    
    p {
      color: #1b5e20;
      font-weight: 500;
      margin: 0;
    }
  }
}

// 深色主题适配
[data-theme="dark"] .button-box-example {
  .example-section {
    background-color: #2a2a2a;
    border-color: #444;
    
    h3 {
      color: #ccc;
    }
  }
  
  .result-section {
    background-color: #1e3a1e;
    border-left-color: #66bb6a;
    
    h3 {
      color: #81c784;
    }
    
    p {
      color: #a5d6a7;
    }
  }
}
</style>
